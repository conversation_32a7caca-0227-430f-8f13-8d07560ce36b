import 'server-only';
import { cache } from 'react';
import { getSession } from '@/app/lib/session';
import { redirect } from 'next/navigation';

// Verify session and return user data
export const verifySession = cache(async () => {
  const session = await getSession();
  
  if (!session?.userId) {
    redirect('/login');
  }
  
  return {
    isAuth: true,
    userId: session.userId,
    email: session.email,
    preferences: session.preferences || {}
  };
});

// Get user's weather preferences
export const getUserPreferences = cache(async () => {
  const userData = await verifySession();
  return userData.preferences || {
    defaultLayer: 'temperature',
    defaultLocation: null,
    savedLocations: []
  };
});