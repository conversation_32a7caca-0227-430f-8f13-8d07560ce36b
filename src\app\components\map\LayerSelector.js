"use client";

import { WEATHER_LAYERS } from "@/app/utils/constants";

/**
 * LayerSelector Component
 * 
 * Provides a dropdown to select different weather map layers
 * 
 * @param {Object} props - Component props
 * @param {string} props.activeLayer - The currently active weather layer
 * @param {Function} props.onLayerChange - Function to call when layer selection changes
 * @returns {JSX.Element} - The rendered LayerSelector component
 */
export default function LayerSelector({ activeLayer, onLayerChange }) {
  return (
    <div className="layer-control">
      <select
        value={activeLayer}
        onChange={(e) => onLayerChange(e.target.value)}
        className="layer-select"
        aria-label="Select weather layer"
      >
        {Object.entries(WEATHER_LAYERS).map(([key, layer]) => (
          <option key={key} value={key}>
            {layer.name}
          </option>
        ))}
      </select>
    </div>
  );
}
