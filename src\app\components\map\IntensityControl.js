"use client";

/**
 * IntensityControl Component
 * 
 * Provides a slider to control the intensity (opacity) of the weather layer
 * 
 * @param {Object} props - Component props
 * @param {number} props.intensity - The current intensity value (0-1)
 * @param {Function} props.onIntensityChange - Function to call when intensity changes
 * @returns {JSX.Element} - The rendered IntensityControl component
 */
export default function IntensityControl({ intensity, onIntensityChange }) {
  // Convert intensity to percentage for display
  const intensityPercentage = Math.round(intensity * 100);
  
  return (
    <div className="intensity-control">
      <label htmlFor="intensity-slider" className="intensity-label">
        Layer Intensity: {intensityPercentage}%
      </label>
      <input
        id="intensity-slider"
        type="range"
        min="10"
        max="100"
        value={intensityPercentage}
        onChange={(e) => onIntensityChange(Number(e.target.value) / 100)}
        className="intensity-slider"
        aria-label="Adjust layer intensity"
      />
    </div>
  );
}
