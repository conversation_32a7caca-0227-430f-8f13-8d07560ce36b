/**
 * Meather Wap Global Styles
 *
 * This file contains global styles for the Meather Wap application
 */

@import "tailwindcss";

/**
 * Root variables
 */
:root {
  --background: #ffffff;
  --foreground: #171717;
}

/**
 * Theme configuration
 */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/**
 * Dark mode preferences
 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/**
 * Base styles
 */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/**
 * Layout utilities
 */
.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.transition {
  transition: all 0.3s ease;
}

/**
 * Header styles
 */
header.sticky {
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Add padding to the body to prevent content from being hidden under sticky header */
body {
  scroll-padding-top: 80px; /* Adjust based on your header height */
}

/**
 * Landing page styles
 */
.hero-image-placeholder {
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px dashed rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(59, 130, 246, 0.8);
  font-weight: 500;
}

/**
 * Map component styles
 */
/* Container for the map */
.map-container {
  height: 100%;
  width: 100%;
  position: relative;
}

.leaflet-container {
  height: 100%;
  width: 100%;
}

/* Status messages */
.loading-message,
.error-message {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: white;
  padding: 8px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.error-message {
  background: #ffdddd;
  color: #ff0000;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  font-size: 1.2rem;
}

/* Layer selector control */
.layer-control {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.layer-select {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

/* Intensity control */
.intensity-control {
  position: absolute;
  top: 60px;
  right: 10px;
  z-index: 1000;
  background: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  width: 200px;
}

.intensity-label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
}

.intensity-slider {
  width: 100%;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: #ddd;
  outline: none;
  border-radius: 4px;
  cursor: pointer;
}

.intensity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #4a4a4a;
  border-radius: 50%;
  cursor: pointer;
}

.intensity-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #4a4a4a;
  border-radius: 50%;
  cursor: pointer;
}

/* Legend styles */
.legend {
  position: absolute;
  bottom: 30px;
  right: 10px;
  z-index: 1000;
  background: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  max-height: calc(100% - 100px);
  overflow-y: auto;
  min-width: 150px;
  max-width: 200px;
  font-size: 12px;
}

.layer-select:hover {
  border-color: #999;
}

.legend h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border: 1px solid #ccc;
}

.legend-label {
  flex: 1;
}

/* Make legend scrollable on small screens */
@media (max-height: 600px) {
  .legend {
    max-height: 200px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .legend {
    background: #333;
    color: #fff;
  }

  .legend-color {
    border-color: #666;
  }
}
