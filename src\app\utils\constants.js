/**
 * Constants for the Meather Wap application
 * This file centralizes configuration values used throughout the application
 */

// OpenWeatherMap API key from environment variables
export const OPENWEATHERMAP_API_KEY =
  process.env.NEXT_PUBLIC_OPENWEATHERMAP_API_KEY;

// Logo paths
export const LOGOS = {
  WEATHER_MAP: "/weather map logo.png",
  MEATHER_WAP: "/meather wap logo.png",
};

// Words for the typing animation
export const TYPING_WORDS = ["Weather Map", "Meather Wap"];

// Weather layer configurations for the map
export const WEATHER_LAYERS = {
  temperature: {
    url: `https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=${OPENWEATHERMAP_API_KEY}`,
    name: "Temperature",
  },
  precipitation: {
    url: `https://tile.openweathermap.org/map/precipitation_new/{z}/{x}/{y}.png?appid=${OPENWEATHERMAP_API_KEY}`,
    name: "Precipitation",
  },
};

// Gradient style for progress bar
export const gradientStyle = {
  backgroundImage:
    "linear-gradient(to right, " +
    "#f44c49, " + // >40°C
    "#f46859, " + // 35°C
    "#f48159, " + // 30°C
    "#f4a85e, " + // 25°C
    "#fee270, " + // 15°C
    "#fee892, " + // 10°C
    "#fef8ae, " + // 5°C
    "#ffffd0, " + // 0°C
    "#c2eaff, " + // -5°C
    "#ace1fd, " + // -10°C
    "#67a3de, " + // -20°C
    "#2c6abb, " + // -35°C
    "#9f55b5" + // -40°C
    ")",
};

// Legend configurations for each weather layer
export const LEGEND_CONFIGS = {
  temperature: {
    title: "Temperature (°C)",
    grades: [
      { color: "#f44c49", label: "> 40°C" },
      { color: "#f46859", label: "35°C" },
      { color: "#f48159", label: "30°C" },
      { color: "#f4a85e", label: "25°C" },
      { color: "#fee270", label: "15°C" },
      { color: "#fee892", label: "10°C" },
      { color: "#fef8ae", label: "5°C" },
      { color: "#ffffd0", label: "0°C" },
      { color: "#c2eaff", label: "-5°C" },
      { color: "#ace1fd", label: "-10°C" },
      { color: "#67a3de", label: "-20°C" },
      { color: "#2c6abb", label: "-35°C" },
      { color: "#9f55b5", label: "-40°C" },
    ],
  },
  precipitation: {
    title: "Precipitation (mm)",
    grades: [
      { color: "#641e6f", label: "> 200" },
      { color: "#a92e99", label: "150-200" },
      { color: "#dd3497", label: "100-150" },
      { color: "#f768a1", label: "50-100" },
      { color: "#fa9fb5", label: "20-50" },
      { color: "#fcc5c0", label: "10-20" },
      { color: "#fde0dd", label: "2-10" },
      { color: "#fff7f3", label: "< 2" },
    ],
  },

  pressure: {},
};

// Default map position (London)
export const DEFAULT_MAP_POSITION = [51.505, -0.09];

// Map marker icon configuration
export const MAP_ICON_CONFIG = {
  iconUrl: "/marker-icon.png",
  shadowUrl: "/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
};
