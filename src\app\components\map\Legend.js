"use client";

import { LEGEND_CONFIGS } from "@/app/utils/constants";

/**
 * Legend Component
 *
 * Displays a legend for the active weather layer on the map
 *
 * @param {Object} props - Component props
 * @param {string} props.activeLayer - The currently active weather layer (temperature, precipitation, clouds)
 * @returns {JSX.Element} - The rendered Legend component
 */
export default function Legend({ activeLayer }) {
  const config = LEGEND_CONFIGS[activeLayer];

  return (
    <div className="legend">
      <h4>{config.title}</h4>
      {config.grades.map((grade, index) => (
        <div key={index} className="legend-item">
          <span
            className="legend-color"
            style={{ backgroundColor: grade.color }}
          ></span>
          <span className="legend-label">{grade.label}</span>
        </div>
      ))}
    </div>
  );
}
