{"name": "meatherwap", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"framer-motion": "^12.9.2", "jose": "^5.2.0", "leaflet": "^1.9.4", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4"}}