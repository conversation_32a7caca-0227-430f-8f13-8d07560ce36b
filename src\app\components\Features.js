"use client";
import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";

/**
 * FeatureCard Component
 *
 * A single feature card with icon, title, and description
 *
 * @param {Object} props - Component props
 * @param {string} props.title - The feature title
 * @param {string} props.description - The feature description
 * @param {JSX.Element} props.icon - The SVG icon for the feature
 * @returns {JSX.Element} - The rendered FeatureCard component
 */
function FeatureCard({
  title,
  description,
  icon,
  expanded,
  setExpanded,
  index,
}) {
  return (
    <motion.div
      className="bg-white p-6 rounded-lg shadow-md border border-gray-200 hover:cursor-pointer"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5 }}
      whileHover={{
        y: -10,
        boxShadow:
          "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
      onClick={() => {
        setExpanded(expanded === index + 1 ? 0 : index + 1);
      }}
    >
      <motion.div
        className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-4"
        whileHover={{ rotate: 360, backgroundColor: "#e5e7eb" }}
        transition={{ duration: 0.6 }}
      >
        {icon}
      </motion.div>
      <h3 className="text-xl font-semibold mb-2 text-gray-800">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </motion.div>
  );
}

/**
 * Features Component
 *
 * The features section for the landing page
 * Displays multiple feature cards
 *
 * @returns {JSX.Element} - The rendered Features component
 */
export default function Features() {
  const [expanded, setExpanded] = useState(0);
  const features = [
    {
      title: "Real-time Weather Maps",
      description:
        "Access interactive maps showing temperature, precipitation, and cloud coverage with professional-grade accuracy.",
      longDescription:
        "sheesh1 lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
          />
        </svg>
      ),
    },
    {
      title: "Detailed Analytics",
      description:
        "Get comprehensive weather data analysis for informed decision-making in your professional field.",
      longDescription:
        "sheesh 2 lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",

      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      ),
    },
    {
      title: "Historical Data",
      description:
        "Access historical weather patterns and trends to better predict future conditions for your operations.",
      longDescription:
        "sheesh 3lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",

      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
    },
  ];

  return (
    <section id="features" className="py-16 bg-gray-100 overflow-hidden">
      <div className="container mx-auto px-6">
        <motion.h2
          className="text-3xl font-bold text-center mb-12 text-gray-800"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.3 }}
        >
          Professional Weather Features
        </motion.h2>
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              expanded={expanded}
              setExpanded={setExpanded}
              index={index}
            />
          ))}
        </div>
        <AnimatePresence>
          {expanded > 0 ? (
            <motion.div
              className="text-center w-[50%] mx-auto"
              key={expanded}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h1>{features[expanded - 1].longDescription}</h1>
              <motion.div
                key={expanded}
                initial={{ opacity: 0, height: 0, x: -400 }}
                animate={{ opacity: 1, height: "auto", x: 0 }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              ></motion.div>
            </motion.div>
          ) : null}
        </AnimatePresence>

        {/* Map Data Information Section */}
        <motion.div
          className="bg-white mt-20 p-8 rounded-lg shadow-md border border-gray-200 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
        >
          <motion.h3
            className="text-2xl font-bold mb-6 text-gray-800 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Available Weather Data Layers
          </motion.h3>

          <p className="text-gray-600 mb-6 text-center">
            Our interactive map provides access to multiple weather data layers
            that can be toggled with a simple click. Adjust the intensity of
            each layer to customize your view.
          </p>

          <div className="grid md:grid-cols-3 gap-6">
            <motion.div
              className="bg-gray-50 p-4 rounded-lg border border-gray-200 cursor-pointer"
              whileHover={{
                y: -5,
                boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-red-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z"></path>
                    <path d="M12 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-lg text-gray-800">
                  Temperature
                </h4>
              </div>
              <p className="text-gray-600 text-sm">
                View global temperature patterns with our color-coded heat map.
                Ranges from below -40°C to above 40°C with precise gradients.
              </p>
            </motion.div>

            <motion.div
              className="bg-gray-50 p-4 rounded-lg border border-gray-200 cursor-pointer"
              whileHover={{
                y: -5,
                mouse: "pointer",
                boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-blue-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z"></path>
                    <path d="M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-lg text-gray-800">
                  Precipitation
                </h4>
              </div>
              <p className="text-gray-600 text-sm">
                Track rainfall and precipitation levels worldwide. Data ranges
                from light rain (below 2mm) to heavy downpours (above 200mm).
              </p>
            </motion.div>

            <motion.div
              className="bg-gray-50 p-4 rounded-lg border border-gray-200 cursor-pointer"
              whileHover={{
                y: -5,
                boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-gray-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-lg text-gray-800">
                  Cloud Coverage
                </h4>
              </div>
              <p className="text-gray-600 text-sm">
                Monitor cloud formations and coverage percentages across the
                globe. Visualize clear skies (0%) to complete overcast
                conditions (100%).
              </p>
            </motion.div>
          </div>

          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-gray-600 italic mb-4">
              "The ability to toggle between different weather layers and adjust
              their intensity gives our team unprecedented flexibility in
              analyzing meteorological conditions."
            </p>
            <p className="text-gray-700 font-medium">
              — Professional Meteorologist
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
