"use client";

import { useEffect, useState } from "react";
import { Mark<PERSON>, Popup, useMap } from "react-leaflet";
import L from "leaflet";
import { MAP_ICON_CONFIG } from "@/app/utils/constants";

/**
 * LocationMarker Component
 *
 * Displays the user's current location on the map
 * Attempts to get location via geolocation API, falls back to IP-based location
 *
 * @returns {JSX.Element} - The rendered LocationMarker component
 */
export default function LocationMarker() {
  const [position, setPosition] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const map = useMap();

  // Create marker icon
  const icon = L.icon(MAP_ICON_CONFIG);

  useEffect(() => {
    // Try to get user's location using browser geolocation
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setPosition([latitude, longitude]);
          map.flyTo([latitude, longitude], 10);
          setLoading(false);
        },
        (error) => {
          setLoading(false);
          fetchLocationByIP();
        }
      );
    } else {
      // Geolocation not supported, fall back to IP-based location
      fetchLocationByIP();
    }
  }, [map]);

  // Fallback function to get location by IP address
  const fetchLocationByIP = () => {
    fetch("https://ipapi.co/json/")
      .then((res) => res.json())
      .then((data) => {
        const { latitude, longitude } = data;
        setPosition([latitude, longitude]);
        map.flyTo([latitude, longitude], 10);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error fetching IP location:", err);
        setError("Unable to determine your location");
        setLoading(false);
      });
  };

  return (
    <>
      {loading && (
        <div className="loading-message">Getting your location...</div>
      )}
      {error && <div className="error-message">{error}</div>}
      {position && (
        <Marker position={position} icon={icon}>
          <Popup>You are here</Popup>
        </Marker>
      )}
    </>
  );
}
