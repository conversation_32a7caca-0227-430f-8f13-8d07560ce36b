"use client";

import { useEffect, useState } from "react";
import { LOGOS } from "@/app/utils/constants";
import Header from "./Header";
import Hero from "./Hero";
import Features from "./Features";
import About from "./About";
import Story from "./Story";
import Footer from "./Footer";
import Others from "./Others";

/**
 * HomePage Component
 *
 * The main landing page for Meather Wap
 * Features sections for hero, features, about, and team story
 *
 * @returns {JSX.Element} - The rendered HomePage component
 */
export default function HomePage() {
  // State to track if component is loaded on client
  const [isLoaded, setIsLoaded] = useState(false);

  // State to track which logo to display
  const [currentLogo, setCurrentLogo] = useState(LOGOS.WEATHER_MAP);

  // Set isLoaded to true after component mounts
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  /**
   * Handle typing animation completion
   * Switches the logo based on which word is being typed
   *
   * @param {number} wordIndex - Index of the word that was typed
   */
  const handleTypingComplete = (wordIndex) => {
    // Switch between logos based on the word being typed
    if (wordIndex === 0) {
      // "weather map" is typed
      setCurrentLogo(LOGOS.WEATHER_MAP);
    } else {
      // "meather wap" is typed
      setCurrentLogo(LOGOS.MEATHER_WAP);
    }
  };
  return (
    <div className="flex flex-col min-h-screen">
      <Header
        currentLogo={currentLogo}
        onTypingComplete={handleTypingComplete}
      />

      <main className="flex-grow relative">
        <Hero />
        <Features />
        <About />
        <Story />
        <Others />
      </main>

      <Footer />
    </div>
  );
}
