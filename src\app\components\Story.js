"use client";

import Link from "next/link";
import { motion } from "framer-motion";

/**
 * TeamMember Component
 *
 * Displays information about a team member
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Team member's name
 * @param {string} props.bio - Team member's biography
 * @param {string} props.quote - Team member's quote
 * @param {number} props.initialX - Initial X position for animation
 * @returns {JSX.Element} - The rendered TeamMember component
 */
function TeamMember({ name, bio, quote, initialX }) {
  return (
    <motion.div
      className="bg-white flex align-between flex-col p-8 rounded-lg shadow-md border border-gray-200 relative overflow-hidden"
      initial={{ opacity: 0, x: initialX }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5 }}
      whileHover={{
        y: -10,
        boxShadow:
          "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
    >
      <motion.div
        className="absolute top-0 right-0 w-24 h-24 bg-gray-200 rounded-bl-full opacity-20"
        whileHover={{ rotate: 45, scale: 1.2 }}
        transition={{ duration: 0.5 }}
      />
      <div className="relative z-10">
        <h3 className="text-xl font-bold text-gray-800 mb-3">{name}</h3>
        <p className="text-gray-600 mb-4">{bio}</p>
        <p className="text-gray-600 tracking-wider bg-gray-100 p-4 rounded-lg font-light">
          {quote}
        </p>
        <motion.div
          className="w-16 h-1 bg-gray-300 mt-6"
          whileInView={{ width: "30%" }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
        />
      </div>
    </motion.div>
  );
}

/**
 * Story Component
 *
 * The story section for the landing page
 * Displays information about the team and project history
 *
 * @returns {JSX.Element} - The rendered Story component
 */
export default function Story() {
  const teamMembers = [
    {
      name: "Alex",
      bio: "With a background in atmospheric science and a passion for data visualization, Alex brings the meteorological expertise to our team. Their fascination with weather patterns began during childhood, watching storms roll across the horizon.",
      quote:
        '"I\'ve always been captivated by how weather affects our daily lives. Creating tools that help professionals make informed decisions based on weather data is incredibly fulfilling."',
      initialX: -50,
    },
    {
      name: "Jordan",
      bio: "A web development enthusiast with a keen eye for design, Jordan handles the technical architecture of Meather Wap. Their journey into coding began with creating simple weather widgets for personal use.",
      quote:
        '"Combining cutting-edge web technologies with practical applications like weather forecasting creates something truly valuable. I love seeing how our platform helps professionals make better decisions."',
      initialX: 50,
    },
  ];

  return (
    <section id="story" className="py-16 bg-gray-100 overflow-hidden">
      <div className="container mx-auto px-6">
        <motion.h2
          className="text-3xl font-bold text-center mb-12 text-gray-800"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.6 }}
        >
          Our Story
        </motion.h2>

        <div className="max-w-4xl mx-auto">
          <motion.div
            className="mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.7 }}
          >
            <p className="text-gray-600 text-lg mb-6 leading-relaxed">
              Meather Wap was born from the shared passion of two computer
              science students who found common ground in their love for
              meteorology and web development.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12">
            {teamMembers.map((member, index) => (
              <TeamMember
                key={index}
                name={member.name}
                bio={member.bio}
                quote={member.quote}
                initialX={member.initialX}
              />
            ))}
          </div>

          <motion.div
            className="mt-16 text-center bg-gray-200 p-8 rounded-lg"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.7 }}
          >
            <motion.h3
              className="text-xl font-bold text-gray-800 mb-4"
              initial={{ scale: 0.9 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              From Classroom Project to Professional Tool
            </motion.h3>
            <p className="text-gray-600 leading-relaxed">
              What began as a university project quickly evolved into something
              much more significant. After receiving positive feedback from
              professors and industry professionals, we decided to expand
              Meather Wap into a comprehensive platform for weather data
              visualization and analysis. Today, our tools are used by
              professionals across various industries who rely on accurate
              meteorological information for critical decision-making.
            </p>
          </motion.div>

          <motion.div
            className="mt-16 text-center bg-gray-800 p-8 rounded-lg"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.7 }}
          >
            <motion.h3
              className="text-xl font-bold text-white mb-4"
              initial={{ scale: 0.9 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Fund Our Journey
            </motion.h3>
            <p className="text-gray-300 leading-relaxed mb-6">
              Help us continue developing professional-grade weather tools for
              the community. Your support enables us to maintain and enhance our
              services.
            </p>
            <motion.button
              className="bg-gray-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-700 transition"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.open("https://example.com/fund", "_blank")}
            >
              Support Meather Wap
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
