"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer } from "react-leaflet";
import "leaflet/dist/leaflet.css";

// Import components
import Legend from "./map/Legend";
import LocationMarker from "./map/LocationMarker";
import LayerSelector from "./map/LayerSelector";
import IntensityControl from "./map/IntensityControl";

// Import constants
import { DEFAULT_MAP_POSITION, WEATHER_LAYERS } from "@/app/utils/constants";

// Import session management
import { updatePreferences } from "@/app/actions/auth";

/**
 * Map Component
 *
 * The main weather map component that displays:
 * - Base map from OpenStreetMap
 * - Weather overlay layer (temperature or precipitation)
 * - User's current location
 * - Layer selector control
 * - Intensity control slider
 * - Legend for the active layer
 *
 * @param {Object} props - Component props
 * @param {Object} props.userData - User data including preferences
 * @returns {JSX.Element} - The rendered Map component
 */
export default function Map({ userData = null }) {
  // State to track client-side rendering
  const [isMounted, setIsMounted] = useState(false);

  // State for the active weather layer - initialize from user preferences
  const [activeLayer, setActiveLayer] = useState(() => {
    const defaultLayer =
      userData?.preferences?.lastSelectedLayer || "temperature";
    return WEATHER_LAYERS[defaultLayer] ? defaultLayer : "temperature";
  });

  // State for the layer intensity (opacity)
  const [intensity, setIntensity] = useState(1);

  // Handle client-side rendering
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Ensure activeLayer is valid (in case it was previously set to "clouds")
  useEffect(() => {
    if (!WEATHER_LAYERS[activeLayer]) {
      setActiveLayer("temperature");
    }
  }, [activeLayer]);

  // Save layer preference when it changes
  useEffect(() => {
    if (userData?.userId && isMounted) {
      const savePreference = async () => {
        try {
          await updatePreferences(userData.userId, {
            lastSelectedLayer: activeLayer,
          });
        } catch (error) {
          console.error("Failed to save layer preference:", error);
        }
      };

      // Debounce the save operation
      const timeoutId = setTimeout(savePreference, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [activeLayer, userData?.userId, isMounted]);

  // Show loading state until component is mounted on client
  if (!isMounted) {
    return <div className="loading">Loading map...</div>;
  }

  return (
    <div className="map-container">
      {/* Layer selector control */}
      <LayerSelector activeLayer={activeLayer} onLayerChange={setActiveLayer} />

      {/* Intensity control slider */}
      <IntensityControl
        intensity={intensity}
        onIntensityChange={setIntensity}
      />

      {/* Legend for the active layer */}
      <Legend activeLayer={activeLayer} />

      {/* Map container */}
      <MapContainer
        center={DEFAULT_MAP_POSITION}
        zoom={2}
        style={{ height: "100%", width: "100%" }}
      >
        {/* Base map layer */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Weather layer */}
        <TileLayer
          attribution='&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>'
          url={WEATHER_LAYERS[activeLayer].url}
          opacity={intensity}
        />

        {/* User location marker */}
        <LocationMarker />
      </MapContainer>
    </div>
  );
}
