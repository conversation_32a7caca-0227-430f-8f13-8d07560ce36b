"use client";

import Link from "next/link";
import { motion } from "framer-motion";

/**
 * About Component
 *
 * The about section for the landing page
 * Provides information about the Meather Wap platform
 *
 * @returns {JSX.Element} - The rendered About component
 */
export default function About() {
  // Technology stack used in the project
  const technologies = [
    {
      name: "Next.js",
      description: "React framework for server-rendered applications",
      version: "15.3.1",
      category: "frontend",
      url: "https://nextjs.org/",
    },
    {
      name: "React",
      description: "JavaScript library for building user interfaces",
      version: "19.0.0",
      category: "frontend",
      url: "https://react.dev/",
    },
    {
      name: "Leaflet",
      description: "Open-source JavaScript library for interactive maps",
      version: "1.9.4",
      category: "mapping",
      url: "https://leafletjs.com/",
    },
    {
      name: "Framer Motion",
      description: "Production-ready animation library for React",
      version: "12.9.2",
      category: "frontend",
      url: "https://www.framer.com/motion/",
    },
    {
      name: "TailwindCSS",
      description: "Utility-first CSS framework",
      version: "4.0.0",
      category: "styling",
      url: "https://tailwindcss.com/",
    },
  ];

  // APIs used in the project
  const apis = [
    {
      name: "OpenWeatherMap",
      description:
        "Provides weather data layers including temperature, precipitation, and cloud coverage",
      icon: "🌦️",
      url: "https://openweathermap.org/api",
    },
    {
      name: "OpenStreetMap",
      description: "Provides the base map tiles for our interactive map",
      icon: "🗺️",
      url: "https://www.openstreetmap.org/",
    },
    {
      name: "ipapi.co",
      description:
        "Fallback geolocation service when browser geolocation is unavailable",
      icon: "📍",
      url: "https://ipapi.co/",
    },
  ];

  return (
    <section
      id="about"
      className="py-16 overflow-hidden bg-gray-900 text-white"
    >
      <div className="container mx-auto px-6">
        <motion.h2
          className="text-3xl font-bold mb-10 text-center text-gray-100"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
        >
          About Meather Wap
        </motion.h2>

        <motion.div
          className="max-w-3xl mx-auto text-center mb-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <p className="text-gray-300 text-lg mb-4">
            Meather Wap provides professional-grade weather data for industries
            that depend on accurate meteorological information. Our platform is
            designed for professionals in agriculture, transportation,
            construction, and more.
          </p>
          <p className="text-gray-300 text-lg">
            With our advanced mapping technology and real-time data feeds, you
            can make informed decisions that impact your operations and bottom
            line.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Technologies Section */}
          <motion.div
            className="bg-gray-800 rounded-xl p-8 shadow-lg"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.7 }}
            whileHover={{
              boxShadow:
                "0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3)",
            }}
          >
            <motion.h3
              className="text-2xl font-bold mb-6 text-gray-100 border-b border-gray-700 pb-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5 }}
            >
              <span className="inline-block mr-2">💻</span> Technologies
            </motion.h3>
            <div className="space-y-4">
              {technologies.map((tech, index) => (
                <motion.a
                  href={tech.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  key={index}
                  className="bg-gray-700 p-4 rounded-lg block cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  whileHover={{
                    scale: 1.02,
                    backgroundColor: "rgba(75, 85, 99, 0.8)",
                  }}
                >
                  <div className="flex justify-between items-center">
                    <h4 className="text-lg font-semibold text-gray-100 flex items-center">
                      {tech.name}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 ml-2 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </h4>
                    <span className="text-sm font-mono bg-gray-900 px-2 py-1 rounded text-gray-300">
                      v{tech.version}
                    </span>
                  </div>
                  <p className="text-gray-300 text-sm mt-2">
                    {tech.description}
                  </p>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* APIs Section */}
          <motion.div
            className="bg-gray-800 rounded-xl p-8 shadow-lg"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{
              duration: 0.7,
              type: "spring",
              stiffness: 100,
            }}
            whileHover={{
              boxShadow:
                "0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3)",
            }}
          >
            <motion.h3
              className="text-2xl font-bold mb-6 text-gray-100 border-b border-gray-700 pb-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5 }}
            >
              <span className="inline-block mr-2">🔌</span> APIs & Services
            </motion.h3>
            <div className="space-y-4">
              {apis.map((api, index) => (
                <motion.a
                  href={api.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  key={index}
                  className="bg-gray-700 p-4 rounded-lg block cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  whileHover={{
                    scale: 1.02,
                    backgroundColor: "rgba(75, 85, 99, 0.8)",
                  }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{api.icon}</span>
                    <h4 className="text-lg font-semibold text-gray-100 flex items-center">
                      {api.name}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 ml-2 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </h4>
                  </div>
                  <p className="text-gray-300 text-sm">{api.description}</p>
                </motion.a>
              ))}
            </div>
            <motion.p
              className="text-gray-300 mt-6 italic"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              Click on any technology or API to learn more about it.
            </motion.p>
          </motion.div>
        </div>

        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
        >
          <Link
            href="/map"
            className="bg-gray-100 text-gray-900 px-8 py-4 rounded-lg font-medium hover:bg-white transition inline-block"
          >
            Explore Our Weather Map
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
