"use client";

import Link from "next/link";
import { motion } from "framer-motion";

/**
 * Footer Component
 *
 * The footer section for the application
 * Includes quick links and copyright information
 *
 * @returns {JSX.Element} - The rendered Footer component
 */
export default function Footer() {
  const quickLinks = [
    { name: "Features", href: "#features" },
    { name: "About", href: "#about" },
    { name: "Our Story", href: "#story" },
    { name: "Weather Map", href: "/map" },
  ];

  return (
    <motion.footer
      className="bg-gray-800 text-white py-10"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container mx-auto px-6">
        <div className="flex flex-col md:flex-row justify-between">
          <motion.div
            className="mb-6 md:mb-0"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-2xl font-bold mb-4"><PERSON>her Wap</h2>
            <p className="text-gray-400">
              Professional weather data for professionals
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <motion.li
                  key={index}
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  {link.href.startsWith("/") ? (
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition"
                    >
                      {link.name}
                    </Link>
                  ) : (
                    <a
                      href={link.href}
                      className="text-gray-400 hover:text-white transition"
                    >
                      {link.name}
                    </a>
                  )}
                </motion.li>
              ))}
            </ul>
          </motion.div>
        </div>
        <motion.div
          className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <p>
            &copy; {new Date().getFullYear()} Meather Wap. All rights reserved.
          </p>
        </motion.div>
      </div>
    </motion.footer>
  );
}
