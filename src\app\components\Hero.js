"use client";

import Link from "next/link";
import Image from "next/image";
import { motion as m } from "framer-motion";

/**
 * Hero Component
 *
 * The main hero section for the landing page
 * Features headline, description, call-to-action buttons, and an image
 *
 * @returns {JSX.Element} - The rendered Hero component
 */
export default function Hero() {
  return (
    <m.section
      className="bg-gradient-to-b from-gray-800 to-gray-900 text-white py-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.3 }}
    >
      <div className="container mx-auto px-6 flex flex-col md:flex-row items-center">
        <div className="md:w-1/2 mb-10 md:mb-0">
          <m.h2
            className="text-4xl md:text-5xl font-bold mb-6"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.5 }}
          >
            Professional Weather Data at Your Fingertips
          </m.h2>
          <m.p
            className="text-xl mb-8 text-gray-300"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.9 }}
          >
            Access real-time weather maps, forecasts, and analytics designed for
            professionals who rely on accurate meteorological data.
          </m.p>
          <m.div
            className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1.4 }}
          >
            <m.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/map"
                className="bg-gray-100 text-gray-900 px-6 py-3 rounded-lg font-medium text-lg hover:bg-white transition text-center inline-block w-full"
              >
                View Our Map
              </Link>
            </m.div>
            <m.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <a
                href="#features"
                className="border-2 border-gray-400 text-gray-200 px-6 py-3 rounded-lg font-medium text-lg hover:bg-gray-700 hover:border-white hover:text-white transition text-center inline-block w-full"
              >
                Learn More
              </a>
            </m.div>
          </m.div>
        </div>
        <m.div
          className="md:w-1/2 flex justify-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 0.8,
            delay: 0.6,
            type: "spring",
            stiffness: 100,
          }}
        >
          <m.div
            className="w-full max-w-md h-80 bg-gray-800 rounded-lg shadow-xl overflow-hidden relative"
            whileHover={{
              boxShadow:
                "0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3)",
              scale: 1.05,
            }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-gray-700 opacity-50"></div>
            <m.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2, duration: 0.5 }}
            >
              <m.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.2, duration: 1 }}
              >
                <Image
                  src="/heatmap.webp"
                  alt="Heatmap"
                  fill
                  className="mb-4 opacity-80"
                />
              </m.div>
            </m.div>
            <m.div className="absolute bottom-6 inset-x-0 text-center">
              <m.div
                className="text-gray-900 text-xl font-semibold bg-white px-4 py-2 rounded-lg w-fit mx-auto hover:cursor-pointer"
                whileHover={{ scale: 1.05, y: -10 }}
                whileTap={{ scale: 0.995 }}
              >
                <Link href="/map">Interactive Weather Map</Link>
              </m.div>
            </m.div>
          </m.div>
        </m.div>
      </div>
    </m.section>
  );
}
