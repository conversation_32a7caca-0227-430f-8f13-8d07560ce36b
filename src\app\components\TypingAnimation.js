"use client";

import { useState, useEffect } from "react";
import { motion, useAnimation } from "framer-motion";
import { TYPING_WORDS } from "@/app/utils/constants";

/**
 * TypingAnimation Component
 *
 * Creates a typing effect that alternates between "Weather Map" and "Meather Wap"
 * with a blinking cursor and typing/deleting animations
 *
 * @param {Object} props - Component props
 * @param {Function} props.onTypingComplete - Callback function when typing completes
 * @returns {JSX.Element} - The rendered TypingAnimation component
 */
export default function TypingAnimation({ onTypingComplete }) {
  // Add new state to track if animation should continue
  const [shouldAnimate, setShouldAnimate] = useState(true);
  const [text, setText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const [loopNum, setLoopNum] = useState(0);
  const [typingSpeed, setTypingSpeed] = useState(150);

  // Animation controls for the cursor
  const controls = useAnimation();

  // Typing animation effect
  useEffect(() => {
    if (!shouldAnimate) return; // Exit early if animation should stop

    const handleTyping = () => {
      const current = loopNum % TYPING_WORDS.length;
      const fullText = TYPING_WORDS[current];

      // Handle typing or deleting characters
      if (isDeleting) {
        setText(fullText.substring(0, text.length - 1));
        setTypingSpeed(50); // Faster when deleting
      } else {
        setText(fullText.substring(0, text.length + 1));
        setTypingSpeed(150); // Normal speed when typing
      }

      // If completed typing "Meather Wap"
      if (!isDeleting && text === TYPING_WORDS[1]) {
        setShouldAnimate(false); // Stop the animation
        if (onTypingComplete) {
          onTypingComplete(1);
        }
        return;
      }

      // If completed typing the first word
      if (!isDeleting && text === fullText) {
        if (onTypingComplete) {
          onTypingComplete(current);
        }
        setTimeout(() => setIsDeleting(true), 2000);
      }
      // If deleted the word
      else if (isDeleting && text === "") {
        setIsDeleting(false);
        const nextIndex = loopNum + 1;
        setLoopNum(nextIndex);

        if (onTypingComplete) {
          onTypingComplete(nextIndex % TYPING_WORDS.length);
        }

        setTypingSpeed(500);
      }
    };

    // Set up the timer for typing animation
    const timer = setTimeout(handleTyping, typingSpeed);

    // Clean up the timer on unmount or when dependencies change
    return () => clearTimeout(timer);
  }, [text, isDeleting, loopNum, typingSpeed, onTypingComplete, shouldAnimate]);

  // Cursor blinking animation
  useEffect(() => {
    controls.start({
      opacity: [1, 0, 1],
      transition: {
        duration: 1.3,
        repeat: Infinity,
        repeatType: "loop",
      },
    });
  }, [controls]);

  return (
    <div className="flex items-center">
      <span className="text-2xl font-bold text-gray-100">{text}</span>
      <motion.span
        className="h-6 w-1 bg-gray-100 ml-1"
        animate={controls}
        aria-hidden="true"
      />
    </div>
  );
}
