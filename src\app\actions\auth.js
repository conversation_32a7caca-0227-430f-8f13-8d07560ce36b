"use server";

import { createSession, deleteSession } from "@/app/lib/session";
import { redirect } from "next/navigation";

// Mock user database (replace with actual database)
const users = [
  {
    id: "1",
    email: "<EMAIL>",
    password: "password123",
    preferences: {
      defaultLayer: "temperature",
      lastSelectedLayer: "temperature",
      savedLocations: [],
    },
  },
];

export async function login(formData) {
  const email = formData.get("email");
  const password = formData.get("password");

  // Find user (replace with actual authentication)
  const user = users.find((u) => u.email === email && u.password === password);

  if (!user) {
    return { error: "Invalid credentials" };
  }

  // Create session
  await createSession(user);

  // Redirect to map page
  redirect("/map");
}

export async function logout() {
  await deleteSession();
  redirect("/login");
}

export async function updatePreferences(userId, preferences) {
  // In a real app, update the user's preferences in the database
  const userIndex = users.findIndex((u) => u.id === userId);
  if (userIndex !== -1) {
    users[userIndex].preferences = {
      ...users[userIndex].preferences,
      ...preferences,
    };

    // Update the session with new preferences
    await createSession(users[userIndex]);
  }

  return { success: true };
}
